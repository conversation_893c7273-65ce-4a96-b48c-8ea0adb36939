"""
التطبيق الرئيسي لمنصة الكورسات التعليمية
Main Flask application for the educational courses platform
"""

import os
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash, make_response
from flask_cors import CORS
from config import config
import logging
from datetime import datetime, timezone
from utils.firebase_utils import get_firebase_manager
from utils.auth_utils import get_auth_manager
from utils.auth_middleware import (
    api_auth_required, get_current_user, login_required,
    admin_required, instructor_required, student_required, optional_auth
)

def create_app(config_name=None):
    """إنشاء وتكوين تطبيق Flask"""
    
    # إنشاء التطبيق
    app = Flask(__name__)
    
    # تحديد بيئة التشغيل
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # تطبيق الإعدادات
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تكوين CORS للأمان
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # إنشاء المجلدات المطلوبة
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    # تهيئة Firebase
    firebase_manager = get_firebase_manager()
    if firebase_manager.initialize(app.config):
        app.logger.info('تم تهيئة Firebase بنجاح')
        # تهيئة هيكل قاعدة البيانات الأساسي
        if firebase_manager.initialize_database_structure():
            app.logger.info('تم تهيئة هيكل قاعدة البيانات')
        else:
            app.logger.warning('فشل في تهيئة هيكل قاعدة البيانات')
    else:
        app.logger.error('فشل في تهيئة Firebase')

    # تسجيل بدء التطبيق
    app.logger.info(f'تم بدء تشغيل {app.config["PLATFORM_NAME"]} في بيئة {config_name}')
    
    # ===== ROUTES =====
    
    @app.route('/')
    @optional_auth()
    def index():
        """الصفحة الرئيسية"""
        return render_template('index.html',
                             platform_name=app.config['PLATFORM_NAME'])

    @app.route('/login')
    def login():
        """صفحة تسجيل الدخول"""
        # إعادة توجيه المستخدمين المسجلين لوحة التحكم
        current_user = get_current_user()
        if current_user:
            return redirect(url_for('dashboard'))

        return render_template('auth/login.html',
                             platform_name=app.config['PLATFORM_NAME'])

    @app.route('/logout')
    def logout():
        """تسجيل الخروج"""
        response = make_response(redirect(url_for('index')))
        response.set_cookie('auth_token', '', expires=0)
        return response

    @app.route('/dashboard')
    @login_required()
    def dashboard():
        """لوحة التحكم الرئيسية - إعادة توجيه حسب الدور"""
        current_user = get_current_user()
        user_role = current_user.get('role')

        if user_role == 'admin':
            return redirect(url_for('admin_dashboard'))
        elif user_role == 'instructor':
            return redirect(url_for('instructor_dashboard'))
        elif user_role == 'student':
            return redirect(url_for('student_dashboard'))
        else:
            return redirect(url_for('index'))

    @app.route('/admin/dashboard')
    @admin_required()
    def admin_dashboard():
        """لوحة تحكم الأدمن"""
        current_user = get_current_user()
        return render_template('admin/dashboard.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/admin/specializations')
    @admin_required()
    def admin_specializations():
        """صفحة إدارة التخصصات"""
        current_user = get_current_user()
        return render_template('admin/specializations.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/admin/instructors')
    @admin_required()
    def admin_instructors():
        """صفحة إدارة المدرسين"""
        current_user = get_current_user()
        return render_template('admin/instructors.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/admin/students')
    @admin_required()
    def admin_students():
        """صفحة إدارة الطلاب"""
        current_user = get_current_user()
        return render_template('admin/students.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    # ===== API Endpoints لإدارة المدرسين =====

    @app.route('/api/admin/instructors', methods=['GET'])
    @api_auth_required()
    @admin_required()
    def api_get_instructors():
        """API لجلب قائمة المدرسين مع تفاصيلهم وصلاحياتهم"""
        try:
            firebase_manager = get_firebase_manager()

            # جلب جميع المدرسين
            instructors = firebase_manager.get_all_instructors()

            # تطبيق الفلاتر إذا وجدت
            search = request.args.get('search', '').strip()
            specialization_filter = request.args.get('specialization', '').strip()
            status_filter = request.args.get('status', '').strip()

            if search:
                instructors = [
                    i for i in instructors
                    if search.lower() in i.get('full_name', '').lower() or
                       search.lower() in i.get('email', '').lower()
                ]

            if specialization_filter:
                instructors = [
                    i for i in instructors
                    if i.get('specialization_id') == specialization_filter
                ]

            if status_filter:
                if status_filter == 'active':
                    instructors = [i for i in instructors if i.get('active', True)]
                elif status_filter == 'inactive':
                    instructors = [i for i in instructors if not i.get('active', True)]

            return jsonify({
                'success': True,
                'instructors': instructors,
                'total': len(instructors)
            })

        except Exception as e:
            logger.error(f"خطأ في جلب المدرسين: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب بيانات المدرسين'
            }), 500

    @app.route('/api/admin/instructors/<instructor_id>/permissions', methods=['PUT'])
    @api_auth_required()
    @admin_required()
    def api_update_instructor_permissions(instructor_id):
        """API لتحديث صلاحيات مدرس معين"""
        try:
            data = request.get_json()

            if not data or 'permissions' not in data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات الصلاحيات مطلوبة'
                }), 400

            permissions = data['permissions']

            # التحقق من صحة بيانات الصلاحيات
            required_fields = ['can_create_courses', 'can_manage_students', 'allowed_stages', 'can_create_general_courses']
            for field in required_fields:
                if field not in permissions:
                    return jsonify({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    }), 400

            # التحقق من صحة المراحل
            allowed_stages = permissions.get('allowed_stages', [])
            if not isinstance(allowed_stages, list) or not all(isinstance(stage, int) and 1 <= stage <= 6 for stage in allowed_stages):
                return jsonify({
                    'success': False,
                    'message': 'المراحل المسموحة يجب أن تكون قائمة من الأرقام بين 1 و 6'
                }), 400

            firebase_manager = get_firebase_manager()

            # تحديث الصلاحيات
            success = firebase_manager.update_instructor_permissions(instructor_id, permissions)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث صلاحيات المدرس بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث صلاحيات المدرس'
                }), 500

        except Exception as e:
            logger.error(f"خطأ في تحديث صلاحيات المدرس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في تحديث الصلاحيات'
            }), 500

    @app.route('/api/admin/instructors/statistics', methods=['GET'])
    @api_auth_required()
    @admin_required()
    def api_get_instructors_statistics():
        """API لجلب إحصائيات المدرسين"""
        try:
            firebase_manager = get_firebase_manager()

            # جلب الإحصائيات
            statistics = firebase_manager.get_instructors_statistics()

            return jsonify({
                'success': True,
                'statistics': statistics
            })

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات المدرسين: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب الإحصائيات'
            }), 500

    # ===== API Endpoints لإدارة الطلاب =====

    @app.route('/api/admin/students', methods=['GET'])
    @api_auth_required()
    @admin_required()
    def api_get_students():
        """API لجلب قائمة الطلاب مع تفاصيلهم ومعلومات المدرسين"""
        try:
            firebase_manager = get_firebase_manager()

            # جلب جميع الطلاب
            students = firebase_manager.get_all_students()

            # تطبيق الفلاتر إذا وجدت
            search = request.args.get('search', '').strip()
            instructor_filter = request.args.get('instructor', '').strip()
            specialization_filter = request.args.get('specialization', '').strip()
            status_filter = request.args.get('status', '').strip()

            if search:
                students = [
                    s for s in students
                    if search.lower() in s.get('full_name', '').lower() or
                       search.lower() in s.get('email', '').lower() or
                       search.lower() in s.get('telegram_id', '').lower()
                ]

            if instructor_filter:
                if instructor_filter == 'unassigned':
                    students = [s for s in students if not s.get('assigned_instructor')]
                else:
                    students = [s for s in students if s.get('assigned_instructor') == instructor_filter]

            if specialization_filter:
                students = [s for s in students if s.get('specialization_id') == specialization_filter]

            if status_filter:
                students = [s for s in students if s.get('active') == (status_filter == 'active')]

            return jsonify({
                'success': True,
                'students': students,
                'total': len(students)
            })

        except Exception as e:
            logger.error(f"خطأ في جلب الطلاب: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب البيانات'
            }), 500

    @app.route('/api/admin/students/<student_id>/instructor', methods=['PUT'])
    @api_auth_required()
    @admin_required()
    def api_update_student_instructor(student_id):
        """API لتحديث المدرس المسؤول عن طالب معين"""
        try:
            data = request.get_json()
            instructor_id = data.get('instructor_id')

            firebase_manager = get_firebase_manager()

            # التحقق من وجود الطالب
            student = firebase_manager.get_user(student_id)
            if not student or student.get('role') != 'student':
                return jsonify({
                    'success': False,
                    'message': 'الطالب غير موجود'
                }), 404

            # التحقق من وجود المدرس إذا تم تحديده
            if instructor_id:
                instructor = firebase_manager.get_user(instructor_id)
                if not instructor or instructor.get('role') != 'instructor':
                    return jsonify({
                        'success': False,
                        'message': 'المدرس غير موجود'
                    }), 404

            # تحديث المدرس المسؤول
            success = firebase_manager.update_student_instructor(student_id, instructor_id)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث المدرس المسؤول بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث المدرس المسؤول'
                }), 500

        except Exception as e:
            logger.error(f"خطأ في تحديث المدرس المسؤول: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/students/statistics', methods=['GET'])
    @api_auth_required()
    @admin_required()
    def api_get_students_statistics():
        """API لجلب إحصائيات الطلاب"""
        try:
            firebase_manager = get_firebase_manager()
            statistics = firebase_manager.get_students_statistics()

            return jsonify({
                'success': True,
                'statistics': statistics
            })

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الطلاب: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب الإحصائيات'
            }), 500

    @app.route('/api/instructor/students', methods=['GET'])
    @api_auth_required()
    @instructor_required()
    def api_get_instructor_students():
        """API لجلب طلاب المدرس الحالي"""
        try:
            current_user = get_current_user()
            instructor_id = current_user.get('id')

            firebase_manager = get_firebase_manager()
            students = firebase_manager.get_students_by_instructor(instructor_id)

            return jsonify({
                'success': True,
                'students': students,
                'total': len(students)
            })

        except Exception as e:
            logger.error(f"خطأ في جلب طلاب المدرس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب البيانات'
            }), 500

    @app.route('/admin/analytics')
    @admin_required()
    def admin_analytics():
        """صفحة التحليلات"""
        current_user = get_current_user()
        return render_template('admin/analytics.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/instructor/dashboard')
    @instructor_required()
    def instructor_dashboard():
        """لوحة تحكم المدرس"""
        current_user = get_current_user()
        return render_template('dashboard/instructor.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/instructor/courses/create')
    @instructor_required()
    def instructor_create_course():
        """صفحة إنشاء كورس جديد"""
        current_user = get_current_user()
        return render_template('instructor/create_course.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/instructor/courses')
    @instructor_required()
    def instructor_courses():
        """صفحة إدارة كورسات المدرس"""
        current_user = get_current_user()
        return render_template('instructor/courses.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/student/dashboard')
    @student_required()
    def student_dashboard():
        """لوحة تحكم الطالب"""
        current_user = get_current_user()
        return render_template('dashboard/student.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/courses')
    @student_required()
    def courses():
        """صفحة عرض الكورسات - للطلاب المسجلين فقط"""
        current_user = get_current_user()
        return render_template('courses/index.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/specializations')
    @optional_auth()
    def specializations():
        """صفحة التخصصات"""
        current_user = get_current_user()
        return render_template('specializations/index.html',
                             platform_name=app.config['PLATFORM_NAME'],
                             current_user=current_user)

    @app.route('/invite/<link_id>')
    def handle_invitation(link_id):
        """معالجة روابط الدعوة"""
        try:
            from utils.invitation_utils import get_invitation_manager
            from utils.user_creation_utils import UserCreationManager
            import secrets
            import string

            # التحقق من صحة الرابط
            invitation_manager = get_invitation_manager()
            is_valid, link_data, error_message = invitation_manager.validate_invitation_link(link_id)

            if not is_valid:
                return render_template('invitation/error.html',
                                     platform_name=app.config['PLATFORM_NAME'],
                                     error_message=error_message)

            # التحقق من نوع الرابط
            link_type = link_data.get('link_type')

            if link_type == 'student_invite':
                # معالجة دعوة الطلاب
                return render_template('invitation/student_invite.html',
                                     platform_name=app.config['PLATFORM_NAME'],
                                     link_id=link_id,
                                     link_data=link_data)

            elif link_type == 'instructor_signup':
                # معالجة إنشاء حساب المدرس
                return render_template('invitation/instructor_signup.html',
                                     platform_name=app.config['PLATFORM_NAME'],
                                     link_id=link_id,
                                     link_data=link_data)

            else:
                return render_template('invitation/error.html',
                                     platform_name=app.config['PLATFORM_NAME'],
                                     error_message="نوع رابط غير مدعوم")

        except Exception as e:
            app.logger.error(f"خطأ في معالجة رابط الدعوة: {e}")
            return render_template('invitation/error.html',
                                 platform_name=app.config['PLATFORM_NAME'],
                                 error_message="حدث خطأ في النظام")

    @app.route('/api/invitation/student/<link_id>', methods=['POST'])
    def process_student_invitation(link_id):
        """معالجة دعوة الطلاب عبر API"""
        try:
            from utils.invitation_utils import get_invitation_manager
            from utils.user_creation_utils import UserCreationManager

            data = request.get_json()
            telegram_id = data.get('telegram_id', '').strip()

            if not telegram_id:
                return jsonify({
                    'success': False,
                    'message': 'معرف التليجرام مطلوب'
                }), 400

            # التحقق من صحة الرابط
            invitation_manager = get_invitation_manager()
            is_valid, link_data, error_message = invitation_manager.validate_invitation_link(link_id)

            if not is_valid:
                return jsonify({
                    'success': False,
                    'message': error_message
                }), 400

            # التحقق من نوع الرابط
            if link_data.get('link_type') != 'student_invite':
                return jsonify({
                    'success': False,
                    'message': 'هذا الرابط غير صالح للطلاب'
                }), 400

            # التحقق من عدم وجود حساب مسبق
            firebase_manager = get_firebase_manager()
            existing_user = firebase_manager.get_user_by_telegram_id(telegram_id)
            if existing_user:
                return jsonify({
                    'success': False,
                    'message': 'يوجد حساب مسجل مسبقاً بهذا المعرف'
                }), 400

            # إنشاء حساب الطالب
            user_creation_manager = UserCreationManager()
            target_data = link_data.get('target_data', {})

            success, user_data, password, error_msg = user_creation_manager.create_student_account(
                telegram_id=telegram_id,
                telegram_username=data.get('telegram_username', ''),
                specialization_id=target_data.get('specialization_id'),
                instructor_id=target_data.get('instructor_id')
            )

            if success:
                # تحديث استخدام الرابط
                invitation_manager.use_invitation_link(link_id, telegram_id)

                # الحصول على اسم التخصص
                specialization = firebase_manager.get_specialization(target_data.get('specialization_id'))
                specialization_name = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'

                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء الحساب بنجاح',
                    'user_data': {
                        'email': user_data.get('email'),
                        'password': password,
                        'specialization': specialization_name,
                        'platform_url': app.config['PLATFORM_URL']
                    }
                })

            else:
                return jsonify({
                    'success': False,
                    'message': error_msg
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في معالجة دعوة الطلاب: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500
    
    # ===== API ROUTES =====
    
    @app.route('/api/health')
    def health_check():
        """فحص حالة التطبيق"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'platform': app.config['PLATFORM_NAME']
        })
    
    @app.route('/api/config')
    def get_config():
        """الحصول على إعدادات التطبيق العامة"""
        return jsonify({
            'platform_name': app.config['PLATFORM_NAME'],
            'platform_url': app.config['PLATFORM_URL'],
            'debug': app.config['DEBUG']
        })

    @app.route('/api/firebase/status')
    def firebase_status():
        """فحص حالة اتصال Firebase"""
        firebase_manager = get_firebase_manager()
        is_connected = firebase_manager.is_connected()

        return jsonify({
            'firebase_connected': is_connected,
            'timestamp': datetime.now().isoformat(),
            'status': 'connected' if is_connected else 'disconnected'
        })

    # ===== AUTH API ROUTES =====

    @app.route('/api/auth/login', methods=['POST'])
    def api_login():
        """تسجيل الدخول عبر API"""
        try:
            auth_manager = get_auth_manager()

            # الحصول على البيانات
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                }), 400

            email = data.get('email', '').strip()
            password = data.get('password', '')
            remember = data.get('remember', False)

            if not email or not password:
                return jsonify({
                    'success': False,
                    'message': 'البريد الإلكتروني وكلمة المرور مطلوبان'
                }), 400

            # مصادقة المستخدم
            success, user_data, message = auth_manager.authenticate_user(email, password)

            if not success:
                return jsonify({
                    'success': False,
                    'message': message
                }), 401

            # إنشاء جلسة
            token = auth_manager.create_user_session(user_data)
            if not token:
                return jsonify({
                    'success': False,
                    'message': 'فشل في إنشاء الجلسة'
                }), 500

            # إعداد الاستجابة
            response_data = {
                'success': True,
                'message': 'تم تسجيل الدخول بنجاح',
                'user': {
                    'id': user_data.get('user_id'),
                    'email': user_data.get('email'),
                    'role': user_data.get('role'),
                    'first_name': user_data.get('first_name'),
                    'last_name': user_data.get('last_name'),
                    'full_name': f"{user_data.get('first_name', '')} {user_data.get('last_name', '')}".strip()
                },
                'token': token
            }

            response = make_response(jsonify(response_data))

            # إعداد cookie للتوكين
            if remember:
                # تذكرني لمدة 30 يوم
                response.set_cookie('auth_token', token, max_age=30*24*60*60, httponly=True, secure=False)
            else:
                # جلسة عادية
                response.set_cookie('auth_token', token, httponly=True, secure=False)

            return response

        except Exception as e:
            app.logger.error(f"خطأ في تسجيل الدخول: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/auth/logout', methods=['POST'])
    @api_auth_required()
    def api_logout():
        """تسجيل الخروج عبر API"""
        try:
            auth_manager = get_auth_manager()
            current_user = get_current_user()

            # تسجيل الخروج
            auth_manager.logout_user(request.headers.get('Authorization', '').replace('Bearer ', ''))

            response = make_response(jsonify({
                'success': True,
                'message': 'تم تسجيل الخروج بنجاح'
            }))

            # حذف cookie التوكين
            response.set_cookie('auth_token', '', expires=0)

            return response

        except Exception as e:
            app.logger.error(f"خطأ في تسجيل الخروج: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/auth/me', methods=['GET'])
    @api_auth_required()
    def api_get_current_user():
        """الحصول على بيانات المستخدم الحالي"""
        try:
            current_user = get_current_user()

            return jsonify({
                'success': True,
                'user': {
                    'id': current_user.get('user_id'),
                    'email': current_user.get('email'),
                    'role': current_user.get('role'),
                    'first_name': current_user.get('first_name'),
                    'last_name': current_user.get('last_name'),
                    'full_name': current_user.get('full_name'),
                    'telegram_id': current_user.get('telegram_id'),
                    'specialization_id': current_user.get('specialization_id')
                }
            })

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على بيانات المستخدم: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/auth/refresh', methods=['POST'])
    @api_auth_required()
    def api_refresh_token():
        """تجديد التوكين"""
        try:
            from utils.jwt_utils import get_jwt_manager
            jwt_manager = get_jwt_manager()

            # الحصول على التوكين الحالي
            current_token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not current_token:
                current_token = request.cookies.get('auth_token')

            # تجديد التوكين
            new_token = jwt_manager.refresh_token(current_token)
            if not new_token:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تجديد الجلسة'
                }), 401

            response = make_response(jsonify({
                'success': True,
                'message': 'تم تجديد الجلسة بنجاح',
                'token': new_token
            }))

            # تحديث cookie
            response.set_cookie('auth_token', new_token, httponly=True, secure=False)

            return response

        except Exception as e:
            app.logger.error(f"خطأ في تجديد التوكين: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    # ===== SPECIALIZATIONS API =====

    @app.route('/api/specializations', methods=['GET'])
    @optional_auth()
    def api_get_specializations():
        """الحصول على جميع التخصصات"""
        try:
            firebase_manager = get_firebase_manager()
            specializations = firebase_manager.get_all_specializations()

            # إضافة إحصائيات لكل تخصص
            for spec in specializations:
                # عدد المدرسين في هذا التخصص
                instructors = firebase_manager.get_users_by_specialization(spec['id'])
                spec['instructors_count'] = len([u for u in instructors if u.get('role') == 'instructor'])

                # عدد الكورسات في هذا التخصص
                courses = firebase_manager.get_courses_by_specialization(spec['id'])
                spec['courses_count'] = len(courses)

            return jsonify({
                'success': True,
                'specializations': specializations
            })

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على التخصصات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/specializations/<specialization_id>', methods=['GET'])
    @optional_auth()
    def api_get_specialization(specialization_id):
        """الحصول على تخصص محدد"""
        try:
            firebase_manager = get_firebase_manager()
            specialization = firebase_manager.get_specialization(specialization_id)

            if not specialization:
                return jsonify({
                    'success': False,
                    'message': 'التخصص غير موجود'
                }), 404

            return jsonify(specialization)

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على التخصص: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations', methods=['GET'])
    @api_auth_required()
    @admin_required()
    def api_get_admin_specializations():
        """API لجلب جميع التخصصات للأدمن"""
        try:
            firebase_manager = get_firebase_manager()
            specializations = firebase_manager.get_all_specializations()

            return jsonify({
                'success': True,
                'specializations': specializations or []
            })

        except Exception as e:
            logger.error(f"خطأ في جلب التخصصات للأدمن: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في جلب التخصصات'
            }), 500

    @app.route('/api/admin/specializations', methods=['POST'])
    @admin_required()
    def api_create_specialization():
        """إنشاء تخصص جديد"""
        try:
            firebase_manager = get_firebase_manager()
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                }), 400

            # التحقق من البيانات المطلوبة
            required_fields = ['name', 'name_en', 'icon', 'stages']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    }), 400

            # التحقق من عدم وجود تخصص بنفس الاسم
            existing_specs = firebase_manager.get_all_specializations()
            for spec in existing_specs:
                if spec.get('name') == data['name'] or spec.get('name_en') == data['name_en']:
                    return jsonify({
                        'success': False,
                        'message': 'يوجد تخصص بنفس الاسم مسبقاً'
                    }), 400

            # إنشاء نموذج التخصص
            from models.database_models import DatabaseModels
            spec_data = DatabaseModels.create_specialization_model(
                name=data['name'],
                name_en=data['name_en'],
                description=data.get('description', ''),
                icon=data['icon'],
                color=data.get('color', '#667eea'),
                stages=data['stages'],
                active=data.get('active', True),
                icon_type=data.get('icon_type', 'font_awesome'),
                icon_url=data.get('icon_url')
            )

            # حفظ التخصص
            spec_id = firebase_manager.create_specialization(spec_data)

            if spec_id:
                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء التخصص بنجاح',
                    'specialization_id': spec_id
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في إنشاء التخصص'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في إنشاء التخصص: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations/<specialization_id>', methods=['PUT'])
    @admin_required()
    def api_update_specialization(specialization_id):
        """تحديث تخصص موجود"""
        try:
            firebase_manager = get_firebase_manager()
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                }), 400

            # التحقق من وجود التخصص
            existing_spec = firebase_manager.get_specialization(specialization_id)
            if not existing_spec:
                return jsonify({
                    'success': False,
                    'message': 'التخصص غير موجود'
                }), 404

            # تحديث البيانات
            update_data = {
                'name': data.get('name', existing_spec.get('name')),
                'name_en': data.get('name_en', existing_spec.get('name_en')),
                'description': data.get('description', existing_spec.get('description', '')),
                'icon': data.get('icon', existing_spec.get('icon')),
                'color': data.get('color', existing_spec.get('color', '#667eea')),
                'stages': data.get('stages', existing_spec.get('stages', [])),
                'active': data.get('active', existing_spec.get('active', True)),
                'updated_at': datetime.now().isoformat()
            }

            # حفظ التحديثات
            success = firebase_manager.update_specialization(specialization_id, update_data)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث التخصص بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث التخصص'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في تحديث التخصص: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations/<specialization_id>', methods=['DELETE'])
    @admin_required()
    def api_delete_specialization(specialization_id):
        """حذف تخصص"""
        try:
            firebase_manager = get_firebase_manager()

            # التحقق من وجود التخصص
            existing_spec = firebase_manager.get_specialization(specialization_id)
            if not existing_spec:
                return jsonify({
                    'success': False,
                    'message': 'التخصص غير موجود'
                }), 404

            # التحقق من وجود مدرسين أو كورسات مرتبطة
            instructors = firebase_manager.get_users_by_specialization(specialization_id)
            active_instructors = [u for u in instructors if u.get('role') == 'instructor' and u.get('active', True)]

            if active_instructors:
                return jsonify({
                    'success': False,
                    'message': f'لا يمكن حذف التخصص لوجود {len(active_instructors)} مدرس مرتبط به'
                }), 400

            courses = firebase_manager.get_courses_by_specialization(specialization_id)
            if courses:
                return jsonify({
                    'success': False,
                    'message': f'لا يمكن حذف التخصص لوجود {len(courses)} كورس مرتبط به'
                }), 400

            # حذف التخصص
            success = firebase_manager.delete_specialization(specialization_id)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم حذف التخصص بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في حذف التخصص'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في حذف التخصص: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations/<specialization_id>/toggle', methods=['PATCH'])
    @admin_required()
    def api_toggle_specialization_status(specialization_id):
        """تبديل حالة التخصص (نشط/غير نشط)"""
        try:
            firebase_manager = get_firebase_manager()

            # التحقق من وجود التخصص
            existing_spec = firebase_manager.get_specialization(specialization_id)
            if not existing_spec:
                return jsonify({
                    'success': False,
                    'message': 'التخصص غير موجود'
                }), 404

            # تبديل الحالة
            new_status = not existing_spec.get('active', True)
            update_data = {
                'active': new_status,
                'updated_at': datetime.now().isoformat()
            }

            success = firebase_manager.update_specialization(specialization_id, update_data)

            if success:
                status_text = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
                return jsonify({
                    'success': True,
                    'message': f'{status_text} التخصص بنجاح',
                    'active': new_status
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث حالة التخصص'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في تبديل حالة التخصص: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations/statistics', methods=['GET'])
    @admin_required()
    def api_get_specializations_statistics():
        """الحصول على إحصائيات التخصصات"""
        try:
            firebase_manager = get_firebase_manager()

            # جميع التخصصات
            all_specializations = firebase_manager.get_all_specializations()
            total_specializations = len(all_specializations)
            active_specializations = len([s for s in all_specializations if s.get('active', True)])

            # جميع المدرسين
            all_users = firebase_manager.get_all_users()
            instructors = [u for u in all_users if u.get('role') == 'instructor']
            instructors_count = len(instructors)

            # جميع الكورسات
            all_courses = firebase_manager.get_all_courses()
            courses_count = len(all_courses)

            return jsonify({
                'total_specializations': total_specializations,
                'active_specializations': active_specializations,
                'instructors_count': instructors_count,
                'courses_count': courses_count
            })

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على إحصائيات التخصصات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specializations/import', methods=['POST'])
    @admin_required()
    def api_import_specializations():
        """استيراد التخصصات من ملف JSON"""
        try:
            firebase_manager = get_firebase_manager()
            data = request.get_json()

            if not data or 'specializations' not in data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                }), 400

            specializations_data = data['specializations']
            if not isinstance(specializations_data, list):
                return jsonify({
                    'success': False,
                    'message': 'يجب أن تكون التخصصات في شكل مصفوفة'
                }), 400

            imported_count = 0
            errors = []

            for i, spec_data in enumerate(specializations_data):
                try:
                    # التحقق من البيانات المطلوبة
                    if not all(field in spec_data for field in ['name', 'name_en', 'icon']):
                        errors.append(f'التخصص {i+1}: بيانات ناقصة')
                        continue

                    # التحقق من عدم وجود تخصص بنفس الاسم
                    existing_specs = firebase_manager.get_all_specializations()
                    if any(s.get('name') == spec_data['name'] for s in existing_specs):
                        errors.append(f'التخصص {i+1}: يوجد تخصص بنفس الاسم')
                        continue

                    # إنشاء نموذج التخصص
                    from models.database_models import DatabaseModels
                    new_spec_data = DatabaseModels.create_specialization_model(
                        name=spec_data['name'],
                        name_en=spec_data['name_en'],
                        description=spec_data.get('description', ''),
                        icon=spec_data['icon'],
                        color=spec_data.get('color', '#667eea'),
                        stages=spec_data.get('stages', [2, 3, 4]),
                        active=spec_data.get('active', True),
                        icon_type=spec_data.get('icon_type', 'font_awesome'),
                        icon_url=spec_data.get('icon_url')
                    )

                    # حفظ التخصص
                    spec_id = firebase_manager.create_specialization(new_spec_data)
                    if spec_id:
                        imported_count += 1
                    else:
                        errors.append(f'التخصص {i+1}: فشل في الحفظ')

                except Exception as e:
                    errors.append(f'التخصص {i+1}: {str(e)}')

            return jsonify({
                'success': True,
                'message': f'تم استيراد {imported_count} تخصص بنجاح',
                'imported_count': imported_count,
                'errors': errors
            })

        except Exception as e:
            app.logger.error(f"خطأ في استيراد التخصصات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    # ==================== API endpoints للأيقونات ====================

    @app.route('/api/admin/specialization-icons', methods=['GET'])
    @admin_required()
    def api_get_specialization_icons():
        """الحصول على جميع أيقونات التخصصات المرفوعة"""
        try:
            firebase_manager = get_firebase_manager()

            # الحصول على الأيقونات من قاعدة البيانات
            icons_data = firebase_manager.get_all_specialization_icons()

            # إضافة معلومات إضافية لكل أيقونة
            from utils.icon_utils import icon_manager
            for icon in icons_data:
                # التحقق من وجود الملف
                icon_info = icon_manager.get_icon_info(icon.get('filename', ''))
                if icon_info:
                    icon.update(icon_info)
                else:
                    icon['exists'] = False

            return jsonify({
                'success': True,
                'icons': icons_data
            })

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على الأيقونات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specialization-icons/upload', methods=['POST'])
    @admin_required()
    def api_upload_specialization_icon():
        """رفع أيقونة تخصص جديدة"""
        try:
            firebase_manager = get_firebase_manager()
            current_user = get_current_user()

            # التحقق من وجود الملف
            if 'icon_file' not in request.files:
                return jsonify({
                    'success': False,
                    'message': 'لم يتم اختيار ملف'
                }), 400

            file = request.files['icon_file']
            description = request.form.get('description', '')

            # التحقق من صحة الملف
            from utils.icon_utils import icon_manager
            is_valid, validation_message = icon_manager.validate_image_file(file)

            if not is_valid:
                return jsonify({
                    'success': False,
                    'message': validation_message
                }), 400

            # إنتاج اسم ملف فريد
            filename = icon_manager.generate_unique_filename(file.filename)

            # معالجة وحفظ الصورة
            process_result = icon_manager.process_and_save_icon(file, filename)

            if not process_result['success']:
                return jsonify({
                    'success': False,
                    'message': process_result['message']
                }), 500

            # حفظ معلومات الأيقونة في قاعدة البيانات
            from models.database_models import DatabaseModels
            icon_data = DatabaseModels.create_specialization_icon_model(
                filename=filename,
                original_name=file.filename,
                file_size=process_result['original']['file_size'],
                uploaded_by=current_user['id'],
                description=description
            )

            # إضافة معلومات الأحجام
            icon_data['sizes'] = process_result['sizes']

            icon_id = firebase_manager.create_specialization_icon(icon_data)

            if icon_id:
                # إضافة معرف الأيقونة
                icon_data['id'] = icon_id

                return jsonify({
                    'success': True,
                    'message': 'تم رفع الأيقونة بنجاح',
                    'icon': icon_data
                })
            else:
                # حذف الملفات في حالة فشل الحفظ في قاعدة البيانات
                icon_manager.delete_icon_files(filename)
                return jsonify({
                    'success': False,
                    'message': 'فشل في حفظ الأيقونة'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في رفع الأيقونة: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specialization-icons/<icon_id>', methods=['DELETE'])
    @admin_required()
    def api_delete_specialization_icon(icon_id):
        """حذف أيقونة تخصص"""
        try:
            firebase_manager = get_firebase_manager()

            # الحصول على معلومات الأيقونة
            icon_data = firebase_manager.get_specialization_icon(icon_id)
            if not icon_data:
                return jsonify({
                    'success': False,
                    'message': 'الأيقونة غير موجودة'
                }), 404

            # التحقق من عدم استخدام الأيقونة في أي تخصص
            specializations = firebase_manager.get_all_specializations()
            used_in_specs = [spec for spec in specializations
                           if spec.get('icon_type') == 'uploaded' and
                              spec.get('icon_url', '').endswith(icon_data.get('filename', ''))]

            if used_in_specs:
                spec_names = [spec.get('name', '') for spec in used_in_specs]
                return jsonify({
                    'success': False,
                    'message': f'لا يمكن حذف الأيقونة لأنها مستخدمة في التخصصات التالية: {", ".join(spec_names)}'
                }), 400

            # حذف الملفات
            from utils.icon_utils import icon_manager
            files_deleted = icon_manager.delete_icon_files(icon_data.get('filename', ''))

            # حذف من قاعدة البيانات
            if firebase_manager.delete_specialization_icon(icon_id):
                return jsonify({
                    'success': True,
                    'message': 'تم حذف الأيقونة بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في حذف الأيقونة من قاعدة البيانات'
                }), 500

        except Exception as e:
            app.logger.error(f"خطأ في حذف الأيقونة: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/specialization-icons/<icon_id>', methods=['PUT'])
    @admin_required()
    def api_update_specialization_icon(icon_id):
        """تحديث معلومات أيقونة تخصص"""
        try:
            firebase_manager = get_firebase_manager()
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                }), 400

            # التحقق من وجود الأيقونة
            icon_data = firebase_manager.get_specialization_icon(icon_id)
            if not icon_data:
                return jsonify({
                    'success': False,
                    'message': 'الأيقونة غير موجودة'
                }), 404

            # تحديث البيانات المسموحة
            allowed_fields = ['description', 'active']
            update_data = {}

            for field in allowed_fields:
                if field in data:
                    update_data[field] = data[field]

            if update_data:
                update_data['updated_at'] = datetime.now(timezone.utc).isoformat()

                if firebase_manager.update_specialization_icon(icon_id, update_data):
                    return jsonify({
                        'success': True,
                        'message': 'تم تحديث الأيقونة بنجاح'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'فشل في تحديث الأيقونة'
                    }), 500
            else:
                return jsonify({
                    'success': False,
                    'message': 'لا توجد بيانات للتحديث'
                }), 400

        except Exception as e:
            app.logger.error(f"خطأ في تحديث الأيقونة: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/admin/statistics', methods=['GET'])
    @admin_required()
    def api_get_admin_statistics():
        """الحصول على إحصائيات عامة للأدمن"""
        try:
            firebase_manager = get_firebase_manager()

            # جميع المستخدمين
            all_users = firebase_manager.get_all_users()
            total_users = len(all_users)
            total_instructors = len([u for u in all_users if u.get('role') == 'instructor'])
            total_students = len([u for u in all_users if u.get('role') == 'student'])

            # جميع الكورسات
            all_courses = firebase_manager.get_all_courses()
            total_courses = len(all_courses)

            return jsonify({
                'total_users': total_users,
                'total_instructors': total_instructors,
                'total_students': total_students,
                'total_courses': total_courses
            })

        except Exception as e:
            app.logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    # ==================== API للمدرسين ====================

    @app.route('/api/instructor/course-options', methods=['GET'])
    @api_auth_required()
    @instructor_required()
    def api_get_instructor_course_options():
        """API للحصول على خيارات إنشاء الكورسات للمدرس"""
        try:
            firebase_manager = get_firebase_manager()
            current_user = get_current_user()
            user_id = current_user.get('user_id')

            options = firebase_manager.get_instructor_course_options(user_id)

            return jsonify({
                'success': True,
                'data': options
            })

        except Exception as e:
            logger.error(f"خطأ في الحصول على خيارات الكورسات: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/instructor/courses', methods=['GET'])
    @api_auth_required()
    @instructor_required()
    def api_get_instructor_courses():
        """API للحصول على كورسات المدرس"""
        try:
            firebase_manager = get_firebase_manager()
            user_id = session.get('user_id')

            courses = firebase_manager.get_courses_by_instructor(user_id)

            # إضافة معلومات التخصص لكل كورس
            for course in courses:
                if course.get('specialization_id'):
                    specialization = firebase_manager.get_specialization(course['specialization_id'])
                    course['specialization_name'] = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                else:
                    course['specialization_name'] = 'كورس عام'

            return jsonify({
                'success': True,
                'courses': courses
            })

        except Exception as e:
            logger.error(f"خطأ في الحصول على كورسات المدرس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/instructor/courses', methods=['POST'])
    @api_auth_required()
    @instructor_required()
    def api_create_course():
        """API لإنشاء كورس جديد"""
        try:
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'message': 'بيانات الكورس مطلوبة'
                }), 400

            firebase_manager = get_firebase_manager()
            current_user = get_current_user()
            user_id = current_user.get('user_id')

            # التحقق من الحقول المطلوبة
            required_fields = ['title', 'stage', 'status']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    }), 400

            # التحقق من صلاحيات المدرس
            is_general = data.get('is_general', False)
            specialization_id = data.get('specialization_id') if not is_general else None
            stage = int(data['stage'])

            permission_check = firebase_manager.can_instructor_create_course(
                user_id, specialization_id, stage, is_general
            )

            if not permission_check['allowed']:
                return jsonify({
                    'success': False,
                    'message': permission_check['reason']
                }), 403

            # إعداد بيانات الكورس
            course_data = {
                'title': data['title'].strip(),
                'description': data.get('description', '').strip(),
                'instructor_id': user_id,
                'stage': stage,
                'status': data['status'],
                'is_general': is_general,
                'specialization_id': specialization_id,
                'enrollment_count': 0,
                'lesson_count': 0,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            # إنشاء الكورس
            course_id = firebase_manager.create_course(course_data)

            if course_id:
                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء الكورس بنجاح',
                    'course_id': course_id
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في إنشاء الكورس'
                }), 500

        except Exception as e:
            logger.error(f"خطأ في إنشاء الكورس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/instructor/courses/<course_id>', methods=['DELETE'])
    @api_auth_required()
    @instructor_required()
    def api_delete_course(course_id):
        """API لحذف كورس"""
        try:
            firebase_manager = get_firebase_manager()
            user_id = session.get('user_id')

            # التحقق من وجود الكورس والتأكد أنه ملك للمدرس
            course = firebase_manager.get_course(course_id)
            if not course:
                return jsonify({
                    'success': False,
                    'message': 'الكورس غير موجود'
                }), 404

            if course.get('instructor_id') != user_id:
                return jsonify({
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف هذا الكورس'
                }), 403

            # حذف الكورس
            success = firebase_manager.delete_course(course_id)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم حذف الكورس بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في حذف الكورس'
                }), 500

        except Exception as e:
            logger.error(f"خطأ في حذف الكورس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    @app.route('/api/instructor/courses/<course_id>/status', methods=['PUT'])
    @api_auth_required()
    @instructor_required()
    def api_update_course_status(course_id):
        """API لتحديث حالة الكورس"""
        try:
            data = request.get_json()

            if not data or 'status' not in data:
                return jsonify({
                    'success': False,
                    'message': 'حالة الكورس مطلوبة'
                }), 400

            firebase_manager = get_firebase_manager()
            user_id = session.get('user_id')

            # التحقق من وجود الكورس والتأكد أنه ملك للمدرس
            course = firebase_manager.get_course(course_id)
            if not course:
                return jsonify({
                    'success': False,
                    'message': 'الكورس غير موجود'
                }), 404

            if course.get('instructor_id') != user_id:
                return jsonify({
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل هذا الكورس'
                }), 403

            # التحقق من صحة الحالة
            valid_statuses = ['draft', 'published', 'archived']
            new_status = data['status']

            if new_status not in valid_statuses:
                return jsonify({
                    'success': False,
                    'message': 'حالة الكورس غير صحيحة'
                }), 400

            # تحديث الحالة
            update_data = {
                'status': new_status,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            success = firebase_manager.update_course(course_id, update_data)

            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم تحديث حالة الكورس بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'فشل في تحديث حالة الكورس'
                }), 500

        except Exception as e:
            logger.error(f"خطأ في تحديث حالة الكورس: {e}")
            return jsonify({
                'success': False,
                'message': 'حدث خطأ في النظام'
            }), 500

    # ===== ERROR HANDLERS =====
    
    @app.errorhandler(404)
    def not_found_error(error):
        """معالج خطأ 404"""
        return render_template('errors/404.html',
                             platform_name=app.config['PLATFORM_NAME']), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """معالج خطأ 500"""
        app.logger.error(f'خطأ داخلي في الخادم: {error}')
        return render_template('errors/500.html',
                             platform_name=app.config['PLATFORM_NAME']), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        """معالج خطأ 403"""
        return render_template('errors/403.html',
                             platform_name=app.config['PLATFORM_NAME']), 403
    
    # ===== TEMPLATE FILTERS =====
    
    @app.template_filter('datetime')
    def datetime_filter(value):
        """فلتر لتنسيق التاريخ والوقت"""
        if value is None:
            return ""
        return value.strftime('%Y-%m-%d %H:%M:%S')
    
    @app.template_filter('date')
    def date_filter(value):
        """فلتر لتنسيق التاريخ"""
        if value is None:
            return ""
        return value.strftime('%Y-%m-%d')
    
    # ===== CONTEXT PROCESSORS =====
    
    @app.context_processor
    def inject_global_vars():
        """حقن متغيرات عامة في جميع القوالب"""
        return {
            'platform_name': app.config['PLATFORM_NAME'],
            'current_year': datetime.now().year,
            'debug': app.config['DEBUG']
        }
    
    return app

# إنشاء التطبيق
app = create_app()

if __name__ == '__main__':
    # تشغيل التطبيق في وضع التطوير
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=app.config['DEBUG']
    )
